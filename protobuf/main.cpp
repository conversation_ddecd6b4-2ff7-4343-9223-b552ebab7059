#include <iostream>
#include "person.pb.h"

int main() {
    // using example_namespace::Person;
    // 创建一个 Person 对象
    Person person;
    person.set_name("Alice");
    person.set_age(30);

    // 序列化到字符串
    std::string serialized_data;
    person.SerializeToString(&serialized_data);
    std::cout << "Serialized data size: " << serialized_data.size() << std::endl;

    // 反序列化回来
    Person deserialized_person;
    deserialized_person.ParseFromString(serialized_data);

    std::cout << "Name: " << deserialized_person.name() << std::endl;
    std::cout << "Age: " << deserialized_person.age() << std::endl;

    return 0;
}

#include <bits/stdc++.h>
#include <vector>
using namespace std;
#define debug(a) cout << #a << a << endl;
#define int long long
signed main() {
    int t = 7;
    map<int, int> cnt;
    // vector<int>vct(t);
    for (int i = 0; i < t; i++) {
        int num;
        cin >> num;
        cnt[num]++;
    }
    bool has2 = 0;
    int has3_num = 0;
    for (auto pr : cnt) {
        if (pr.second == 2) has2 = 1;
        if (pr.second >= 3) has3_num++;
    }
    int tag=0;
    if(has2&&has3_num)tag=1;
    if(has3_num>=2)tag=1;
    cout<<(tag?"Yes":"No")<<endl;
}
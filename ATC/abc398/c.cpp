#include <bits/stdc++.h>
#include <vector>
using namespace std;
#define debug(a) cout << #a << a << endl;
#define int long long
signed main() {
    map<int, pair<int, vector<int> > > mp;
    int t;
    cin >> t;
    for (int pos = 1; pos <= t; pos++) {
        int num;
        cin >> num;
        mp[num].first++;
        mp[num].second.push_back(pos);
    }
    int ans = -1;
    // debug(ans);
    for (auto p : mp) {
        
        if (p.second.first == 1) ans = p.second.second.at(0);
    }
    // debug(ans);
    cout<<ans;
}
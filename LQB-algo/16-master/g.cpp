/**
 *    author:  whatever1takes
 *    created: 2025/04/14 23:39:56
**/
#include <bits/stdc++.h>
#include <vector>
using namespace std;
#define rep(i, a, n) for (int i = a; i < n; i++)
#define per(i, a, n) for (int i = n - 1; i >= a; i--)
#define int long long
#define endl "\n"
#define pii pair<long long, long long>
string to_string(string s){return '"' + s + '"';}
string to_string(const char *s){return to_string((string)s);}
string to_string(bool b){return (b ? "true" : "false");}
template <typename A, typename B>
string to_string(pair<A, B> p){return "(" + to_string(p.first) + ", " + to_string(p.second) + ")";}
template <typename A>
string to_string(A v)
{
    bool first = true;
    string res = "{";
    for (const auto &x : v)
    {
        if (!first)
        {
            res += ", ";
        }
        first = false;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() { cerr << endl; }
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T)
{
    cerr << " " << to_string(H);
    debug_out(T...);
}
#define debug(...) cerr << "[" << #__VA_ARGS__ << "]:", debug_out(__VA_ARGS__)

signed main()
{
    ios::sync_with_stdio(false);
    cin.tie(0);
    int len,q;
    cin>>len>>q;
    vector<int>h(len);
    vector<int>sub(q);
    rep(i, 0, len)cin>>h[i];
    rep(i, 0, q)cin>>sub[i];
    map<int,set<int>>mp;
    mp[0].insert(0);
    int jg=1;
    rep(i,0,len){
        for(auto num:mp[jg-1]){
            for(auto num2:sub){
                mp[jg].insert(abs(num-num2));
                mp[jg].insert(abs(num+num2));
            }
        }
        jg++;
    }
    vector<int>dp(len+5,1);
    // for(auto)
    int isok=0;
    rep(i,1,len){
        for(int j=0;j<i;j++){
            if(mp[i-j].count(abs(h[j]-h[i]))){
                int tag=1;
                rep(id, j+1, i){
                    // debug(*(--mp[i-id].end()));
                    // if(h[i]>h[id]&&abs(h[i]-h[id])<*(--mp[i-id].end()))tag=0;
                    // if(h[j]>h[id]&&abs(h[j]-h[id])<*(--mp[id-j].end()))tag=0;
                }
                if(tag){dp[i]=max(dp[j]+1,dp[i]);isok=1;}
            }
        }
    }
    // for(auto pr:mp)
    // debug(pr.first,pr.second);
    // debug(mp.size());
    // debug(dp);
    int ans=0;
    if(!isok)cout<<-1;
    else{
        for(auto num:dp)ans=max(ans,num);
        cout<<len-ans;
    }
    
    return 0;
}
/**
 *    author:  whatever1takes
 *    created: 2025/04/14 22:56:05
 **/
#include <algorithm>
#include <bits/stdc++.h>

using namespace std;
#define rep(i, a, n) for (int i = a; i < n; i++)
#define per(i, a, n) for (int i = n - 1; i >= a; i--)
#define int long long
#define endl "\n"
#define pii pair<long long, long long>
string to_string(string s) {
    return '"' + s + '"';
}
string to_string(const char *s) {
    return to_string((string)s);
}
string to_string(bool b) {
    return (b ? "true" : "false");
}
template <typename A, typename B>
string to_string(pair<A, B> p) {
    return "(" + to_string(p.first) + ", " + to_string(p.second) + ")";
}
template <typename A>
string to_string(A v) {
    bool first = true;
    string res = "{";
    for (const auto &x : v) {
        if (!first) {
            res += ", ";
        }
        first = false;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() {
    cerr << endl;
}
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T) {
    cerr << " " << to_string(H);
    debug_out(T...);
}
#define debug(...) cerr << "[" << #__VA_ARGS__ << "]:", debug_out(__VA_ARGS__)
bool cmp(string s1, string s2) {
    string ss1 = s1 + s2, ss2 = s2 + s1;
    return ss1 > ss2;
}
string conbin(int x) {
    string s;
    while (x) {
        s += (x % 2 + '0');
        x /= 2;
    }
    reverse(s.begin(), s.end());
    return s;
}
const int N = 2000005;
int arrlen, pllen;
signed main() {
    ios::sync_with_stdio(false);
    cin.tie(0);
    int t;
    cin >> t;
    vector<string> v;
    rep(i, 1, t + 1) v.push_back(conbin(i));
    sort(v.begin(), v.end(), cmp);
    vector<int> arr(N), pl(N);
    // debug(v);
    string res = "";
    for (auto str : v) res += str;
    int ans = 0;
    int mul = 1;
    // debug(res);
    pl[0] = 1;
    pllen = 1;
    arrlen = 1;
    int pljw = 0, arrjw = 0;
    for (int i = res.size() - 1; i >= 0; i--) {
        if (res[i] == '1') {
            for (int j = 0; j < arrlen + 3; j++) {
                arr[j] += arrjw + pl[j];
                if (arr[j]) arrlen = max(arrlen, j);
                if (arr[j] >= 10)
                    arrjw = 1;
                else
                    arrjw = 0;
                arr[j] %= 10;
            }
        }
        for (int j = 0; j < pllen + 3; j++) {
            pl[j] *= 2;
            pl[j] += pljw;
            if (pl[j]) pllen = max(pllen, j);
            if (pl[j] >= 10)
                pljw = 1;
            else
                pljw = 0;
            pl[j] %= 10;
        }
        // debug(arr, pl);
        // mul*=2;
    }
    // debug(ans);
    // cout << ans;
    string rres = "";
    // debug(arrlen);
    rep(i, 0, arrlen + 3) {
        rres += (char)(arr[i] + '0');
    }
    while (rres.back() == '0') {
        rres.pop_back();
    }
    reverse(rres.begin(), rres.end());
    cout<<(rres);
    return 0;
}
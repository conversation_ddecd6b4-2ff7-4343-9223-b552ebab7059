{"name": "Local: d", "url": "/Users/<USER>/code/LQB-algo/16-master/d.cpp", "tests": [{"id": 1744642679612, "input": "3", "output": ""}, {"id": 1744642753004, "input": "5", "output": ""}, {"id": 1744643123181, "input": "20", "output": ""}, {"id": 1744644183181, "input": "1000", "output": ""}, {"id": 1744644189342, "input": "10000", "output": ""}], "interactive": false, "memoryLimit": 1024, "timeLimit": 3000, "srcPath": "/Users/<USER>/code/LQB-algo/16-master/d.cpp", "group": "local", "local": true}
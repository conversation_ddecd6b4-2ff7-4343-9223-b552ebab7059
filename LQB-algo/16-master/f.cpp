/**
 *    author:  whatever1takes
 *    created: 2025/04/14 23:25:49
**/
#include <bits/stdc++.h>

using namespace std;
#define rep(i, a, n) for (int i = a; i < n; i++)
#define per(i, a, n) for (int i = n - 1; i >= a; i--)
#define int long long
#define endl "\n"
#define pii pair<long long, long long>
string to_string(string s){return '"' + s + '"';}
string to_string(const char *s){return to_string((string)s);}
string to_string(bool b){return (b ? "true" : "false");}
template <typename A, typename B>
string to_string(pair<A, B> p){return "(" + to_string(p.first) + ", " + to_string(p.second) + ")";}
template <typename A>
string to_string(A v)
{
    bool first = true;
    string res = "{";
    for (const auto &x : v)
    {
        if (!first)
        {
            res += ", ";
        }
        first = false;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() { cerr << endl; }
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T)
{
    cerr << " " << to_string(H);
    debug_out(T...);
}
#define debug(...) cerr << "[" << #__VA_ARGS__ << "]:", debug_out(__VA_ARGS__)
string conbin(int x) {
    string s;
    while (x) {
        s += (x % 2 + '0');
        x /= 2;
    }
    reverse(s.begin(), s.end());
    return s;
}
int cntones(int x){
    int cnt=0;
    while (x) {
        if(x%2)cnt++;
        x/=2;

    }
    return cnt;
}
int cntlen(int x){
    int cnt=0;
    if(x==0)return 1;
    while (x) {
        cnt++;
        x/=2;
    }
    return cnt;
}
signed main()
{
    ios::sync_with_stdio(false);
    cin.tie(0);
    int t;
    cin >> t;
    // rep(i,0,t+1){
    //     debug(i,cntlen(i),
    //         cntones(i));
    // }
    int id=0;
    int ans=0;
    while(t-cntlen(id)>=0){
        ans+=cntones(id);
        t-=cntlen(id);

        id++;
    }
    // debug(ans,id,t);
    // debug(conbin(id));
    rep(i,0,t)ans+=conbin(id)[i]=='1'?1:0;
    // debug(ans);
    cout<<ans;
    return 0;
}
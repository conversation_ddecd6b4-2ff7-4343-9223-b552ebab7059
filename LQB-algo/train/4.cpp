#include <bits/stdc++.h>
#include <string>
#include <vector>
using namespace std;
#define endl "\n"
#define int long long
#define rep(i, a, n) for (int i = a; i < n; i++)
string to_string(string s) {
    return '"' + s + '"';
}
string to_string(const char *c) {
    return to_string((string)c);
}
string to_string(bool bl) {
    return bl ? "true" : "false";
}
template <typename A, typename B>
string to_string(pair<A, B> pr) {
    return "(" + to_string(pr.first) + " , " + to_string(pr.second) + ")";
}
template <typename A>
string to_string(A v) {
    string res = "{";
    bool tag = 1;
    for (const auto &x : v) {
        if (!tag) {
            res += ", ";
        }
        tag = 0;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() {
    cout << endl;
}
template <typename Head, typename... Tail>
void debug_out(<PERSON> H, Tail... T) {
    cout << ' ' << to_string(H);
    debug_out(T...);
}
#define debug(...)                        \
    cout << "[" << #__VA_ARGS__ << "]: "; \
    debug_out(__VA_ARGS__);
signed main() {
    ios::sync_with_stdio(false);
    cin.tie(0);
    cout.tie(0);

    int t;
    cin >> t;
    vector<int> vct(t);
    map<int, vector<int>> mp;
    rep(i, 0, t) {
        cin >> vct[i];
        if(i)vct[i]+=vct[i-1];
        vct[i] %= 7;
        mp[vct[i]].push_back(i);
    }
    // for(auto p:mp){
    //     debug(p.first,p.second);
    // }
    int ans = -1;
    for (auto p : mp) {
        auto v=p.second;
        if(v.size()){
            if(p.first){
                int tempans=v.back()-v.front();
                ans=max(ans,tempans);
            }
            else{
                ans=max(ans,v.back()+1);
            }
            
        }
    }
    if(ans!=-1)
    cout<<ans;
    else cout<<0;
    // debug(ans);
}
#include <bits/stdc++.h>
#include <string>
using namespace std;
#define endl "\n"
#define int long long
string to_string(string s) {
    return '"' + s + '"';
}
string to_string(const char *c) {
    return to_string((string)c);
}
string to_string(bool bl) {
    return bl ? "true" : "false";
}
template <typename A, typename B>
string to_string(pair<A, B> pr) {
    return "(" + to_string(pr.first) + " , " + to_string(pr.second) + ")";
}
template <typename A>
string to_string(A v) {
    string res = "{";
    bool tag = 1;
    for (const auto &x : v) {
        if (!tag) {
            res += ", ";
        }
        tag = 0;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() {
    cout << endl;
}
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T) {
    cout << ' ' << to_string(H);
    debug_out(T...);
}
#define debug(...)                        \
    cout << "[" << #__VA_ARGS__ << "]: "; \
    debug_out(__VA_ARGS__);
signed main() {
    ios::sync_with_stdio(false);
    cin.tie(0);
    cout.tie(0);
    
    int t;
    cin>>t;
    while (t--) {
        int num;
        string s;
        cin>>num>>s;
        string s1="A",s2="B";
        while (s1.size()!=2*num) {
            if(s1.back()=='A')s1.push_back('B');
            else s1.push_back('A');
            if(s2.back()=='A')s2.push_back('B');
            else s2.push_back('A');
        }
        // debug(s1,s2);
        int dif1=0,dif2=0;
        for(int i=0;i<s1.size();i++){
            if(s1[i]!=s[i])dif1++;
            if(s2[i]!=s[i])dif2++;
        }
        // debug(dif1,dif2);
        cout<<min(dif1,dif2)/2<<endl;
    }
}
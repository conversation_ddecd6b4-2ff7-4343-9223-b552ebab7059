#include <bits/stdc++.h>
#include <string>
#include <vector>
using namespace std;
#define endl "\n"
#define int unsigned long long
#define rep(i, a, n) for (int i = a; i < n; i++)
string to_string(string s) {
    return '"' + s + '"';
}
string to_string(const char *c) {
    return to_string((string)c);
}
string to_string(bool bl) {
    return bl ? "true" : "false";
}
template <typename A, typename B>
string to_string(pair<A, B> pr) {
    return "(" + to_string(pr.first) + " , " + to_string(pr.second) + ")";
}
template <typename A>
string to_string(A v) {
    string res = "{";
    bool tag = 1;
    for (const auto &x : v) {
        if (!tag) {
            res += ", ";
        }
        tag = 0;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() {
    cout << endl;
}
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T) {
    cout << ' ' << to_string(H);
    debug_out(T...);
}
#define debug(...)                        \
    cout << "[" << #__VA_ARGS__ << "]: "; \
    debug_out(__VA_ARGS__);
signed main() {
    ios::sync_with_stdio(false);
    cin.tie(0);
    cout.tie(0);

    int T;
    cin >> T;
    while (T--) {
        int n, m, qry;
        cin >> n >> m >> qry;
        vector<vector<int>> v(n + 1, vector<int>(m + 1, 0));
        vector<vector<int>> qzh = v;
        rep(i, 1, n + 1) {
            rep(j, 1, m + 1) {
                cin >> v[i][j];
            }
        }
        rep(i, 1, n + 1) {
            rep(j, 1, m + 1) {
                qzh[i][j] = qzh[i - 1][j] + qzh[i][j - 1] - qzh[i - 1][j - 1] + v[i][j];
                // cout<<qzh[i][j]<<' ';
            }
            // cout<<endl;
        }
        // int qry;
        int ans=0;
        rep(i,0,qry){
            int u,v,x,y;
            cin>>u>>v>>x>>y;
            int temp=qzh[x][y]-qzh[u-1][y]-qzh[x][v-1]+qzh[u-1][v-1];
            ans^=temp;

        }
        cout<<ans<<endl;

    }
}
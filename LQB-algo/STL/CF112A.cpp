#include <bits/stdc++.h>
#include <vector>
using namespace std;
int main() {
    int queues;
    cin >> queues;
    string op;
    vector<int> v[12];
    map<int, int> length;
    int ans = 0;
    while (cin >> op) {
        int index;
        if (op == "query") {
            cout << ans << endl;
        } else {
            cin >> index;
            if (op == "add")
                v[0].push_back(0);
            if (op == "sync") {
                if (v[index].size() < v[0].size()) {
                    v[index].push_back(v[0][v[index].size()]);
                    length[v[index].size()]++;
                    if (length[v[index].size()] == queues - 1)
                        ans = max(ans, (int)v[index].size());
                }
            }
        }
    }

    return 0;
}
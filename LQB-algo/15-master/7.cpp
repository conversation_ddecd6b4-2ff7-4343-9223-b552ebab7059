#include <bits/stdc++.h>
using namespace std;
const int N = 1e3 + 5;
bool con[N][N];
int main() {
    // 请在此输入您的代码
    int t;
    cin >> t;
    vector<int> v(t + 5);
    vector<int> fa(t + 5);
    for (int i = 0; i < t; i++) {
        cin >> v[i];
    }
    for (int i = 0; i < t; i++) {
        int num;
        cin >> num;
        if (num != -1) {
            int from = i;
            int to = num;
            con[from][to] = 1;
            con[to][from] = 1;
        }
    }
    int ans = 0;
    for (int i = 0; i < t; i++) {
        for (int j = i + 1; j < t; j++) {
            if (con[i][j] == false) ans = max(ans, v[i] ^ v[j]);
        }
    }
    cout << ans;
    return 0;
}
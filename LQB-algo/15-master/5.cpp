/**
 *    author:  whatever1takes
 *    created: 2025/04/04 21:11:44
 **/
#include <bits/stdc++.h>

using namespace std;
#define rep(i, a, n) for (int i = a; i < n; i++)
#define per(i, a, n) for (int i = n - 1; i >= a; i--)
#define int long long
#define endl "\n"
#define pii pair<long long, long long>
string to_string(string s) {
    return '"' + s + '"';
}
string to_string(const char *s) {
    return to_string((string)s);
}
string to_string(bool b) {
    return (b ? "true" : "false");
}
template <typename A, typename B>
string to_string(pair<A, B> p) {
    return "(" + to_string(p.first) + ", " + to_string(p.second) + ")";
}
template <typename A>
string to_string(A v) {
    bool first = true;
    string res = "{";
    for (const auto &x : v) {
        if (!first) {
            res += ", ";
        }
        first = false;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() {
    cerr << endl;
}
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T) {
    cerr << " " << to_string(H);
    debug_out(T...);
}
#define debug(...) cerr << "[" << #__VA_ARGS__ << "]:", debug_out(__VA_ARGS__)
const int N = 2e6 + 5;
signed main() {
    ios::sync_with_stdio(false);
    cin.tie(0);
    int t, len;
    cin >> t >> len;

    vector<int> pos(N), neg(N);
    vector<int> posnum(N), negnum(N);
    rep(i, 0, t) {
        int num;
        cin >> num;
        if (num > 0)
            posnum[num] += 1;
        else if (num < 0)
            negnum[-num] += 1;
        else {
            posnum[0] += 1;
            negnum[0] += 1;
        }
    }
    pos[0]=posnum[0];
    neg[0]=negnum[0];
    rep(i, 1, N) {
        pos[i] = pos[i - 1] + posnum[i];
        // int neg_id=-i;
        neg[i] = neg[i - 1] + negnum[i];
    }
    // rep(i,0,10){
    //     debug(posnum[i],i);
    //     debug(negnum[i],i);
    //     debug(pos[i],i);
    //     debug(neg[i],i);
    // }
    int ans = 0;
    for (int i = 0; i <= len; i++) {
        int ri = i;
        int le = len - ri - ri;
        if (ri >= 0 && le >= 0) {
            ans = max(ans, pos[ri] + neg[le] - posnum[0]);
            ans = max(ans, pos[le] + neg[ri] - negnum[0]);
        }
    }
    ans = max(ans, pos[len]);
    ans = max(ans, neg[len]);
    cout << ans;
    return 0;
}
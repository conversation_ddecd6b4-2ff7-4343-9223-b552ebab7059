/**
 *    author:  whatever1takes
 *    created: 2025/03/31 16:31:22
**/
#include <bits/stdc++.h>
#include <string>

using namespace std;
#define rep(i, a, n) for (int i = a; i < n; i++)
#define per(i, a, n) for (int i = n - 1; i >= a; i--)
#define int long long
#define endl "\n"
#define pii pair<long long, long long>
string to_string(string s){return '"' + s + '"';}
string to_string(const char *s){return to_string((string)s);}
string to_string(bool b){return (b ? "true" : "false");}
template <typename A, typename B>
string to_string(pair<A, B> p){return "(" + to_string(p.first) + ", " + to_string(p.second) + ")";}
template <typename A>
string to_string(A v)
{
    bool first = true;
    string res = "{";
    for (const auto &x : v)
    {
        if (!first)
        {
            res += ", ";
        }
        first = false;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() { cerr << endl; }
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T)
{
    cerr << " " << to_string(H);
    debug_out(T...);
}
#define debug(...) cerr << "[" << #__VA_ARGS__ << "]:", debug_out(__VA_ARGS__)

signed main()
{
    ios::sync_with_stdio(false);
    cin.tie(0);
    int t;
    cin >> t;
    string s=to_string(t);
    debug(s.substr(s.size()-2,2));
    int last=stoll(s.substr(s.size()-2,2));
    int multi=last*(last-1)/2;
    debug(multi);

    int mul=1;
    int sum=0;
    int cnt1=0,cnt2=0,cnt3=0,cnt4=0,cnt5=0;
    for(int i=1;i<=100;i++){
        mul*=i;
        sum+=i;
        mul%=100;
        sum%=100;
        if(mul==0&&sum==0)cnt1++;
        // debug(mul,sum,i);
        
    }mul=1;
    sum=0;
    for(int i=1;i<=200;i++){
        mul*=i;
        sum+=i;
        mul%=100;
        sum%=100;
        if(mul==0&&sum==0)cnt2++;
        // debug(mul,sum,i);
        
    }mul=1;
    sum=0;
    for(int i=1;i<=300;i++){
        mul*=i;
        sum+=i;
        mul%=100;
        sum%=100;
        if(mul==0&&sum==0){cnt3++;}
        debug(i,mul,sum);
    }mul=1;
    sum=0;
    for(int i=1;i<=400;i++){
        mul*=i;
        sum+=i;
        mul%=100;
        sum%=100;
        if(mul==0&&sum==0)cnt4++;
        
    }
    mul=1;
    sum=0;
    for(int i=1;i<=100000;i++){
        mul*=i;
        sum+=i;
        mul%=100;
        sum%=100;
        if(mul==0&&sum==0)cnt5++;
        
    }
    debug(cnt1,cnt2,cnt3,cnt4,cnt5);
    debug(2024041331404200/50);
    cout<<(40480826628084+2);
    return 0;
}
/**
 *    author:  whatever1takes
 *    created: 2025/03/31 16:56:53
 **/
#include <bits/stdc++.h>
#include <vector>

using namespace std;
#define rep(i, a, n) for (int i = a; i < n; i++)
#define per(i, a, n) for (int i = n - 1; i >= a; i--)
#define int long long
#define endl "\n"
#define pii pair<long long, long long>
string to_string(string s) {
    return '"' + s + '"';
}
string to_string(const char *s) {
    return to_string((string)s);
}
string to_string(bool b) {
    return (b ? "true" : "false");
}
template <typename A, typename B>
string to_string(pair<A, B> p) {
    return "(" + to_string(p.first) + ", " + to_string(p.second) + ")";
}
template <typename A>
string to_string(A v) {
    bool first = true;
    string res = "{";
    for (const auto &x : v) {
        if (!first) {
            res += ", ";
        }
        first = false;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() {
    cerr << endl;
}
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T) {
    cerr << " " << to_string(H);
    debug_out(T...);
}
#define debug(...) cerr << "[" << #__VA_ARGS__ << "]:", debug_out(__VA_ARGS__)
int value(string a, string b) {
    int ans = 0;
    int cnt = 0;
    for (int i = 0; i < a.size(); i++) {
        if (a[i] == b[i]) {
            cnt++;
            ans = max(ans, cnt);
        } else
            cnt = 0;
    }

    int be = 0, ed = 0;
    for (int i = 0; i < a.size(); i++) {
        if (a[i] == b[i]) {
            be++;
        } else
            break;
    }
    for (int i = a.size() - 1; i >= 0; i--) {
        if (a[i] == b[i]) {
            ed++;
        } else
            break;
    }
    return max(ed + be, ans);
}
signed main() {
    ios::sync_with_stdio(false);
    cin.tie(0);
    int t, len;
    cin >> t >> len;
    vector<string> vct;
    while (t--) {
        string s;
        cin >> s;
        vct.push_back(s);

        debug(" ");
    }
    debug(vct.size());
    vector<vector<int>> v(205, vector<int>(205, 0));
    for (int i = 0; i < vct.size(); i++) {
        for (int j = i + 1; j < vct.size(); j++) {
            debug(i, j, vct[i], vct[j]);
            string s = vct[i];
            int maxval = 0;
            for (int id = 0; id < len; id++) {
                string ed = s.substr(id, len - id);
                string fr = s.substr(0, id);
                string con = ed + fr;
                debug(con);
                maxval = max(maxval, value(con, vct[j]));
            }
            debug(maxval);
            v[i][j] = maxval;
        }
    }
    return 0;
}
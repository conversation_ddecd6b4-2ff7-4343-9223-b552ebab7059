/**
 *    author:  whatever1takes
 *    created: 2025/03/31 16:12:45
 **/
#include <algorithm>
#include <bits/stdc++.h>
#include <fstream>
#include <string>
#include <vector>

using namespace std;
#define rep(i, a, n) for (int i = a; i < n; i++)
#define per(i, a, n) for (int i = n - 1; i >= a; i--)
#define int long long
#define endl "\n"
#define pii pair<long long, long long>
string to_string(string s) {
    return '"' + s + '"';
}
string to_string(const char *s) {
    return to_string((string)s);
}
string to_string(bool b) {
    return (b ? "true" : "false");
}
template <typename A, typename B>
string to_string(pair<A, B> p) {
    return "(" + to_string(p.first) + ", " + to_string(p.second) + ")";
}
template <typename A>
string to_string(A v) {
    bool first = true;
    string res = "{";
    for (const auto &x : v) {
        if (!first) {
            res += ", ";
        }
        first = false;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() {
    cerr << endl;
}
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T)  {   cerr << " " << to_string(H);
    debug_out(T...);
}
#define debug(...) cerr << "[" << #__VA_ARGS__ << "]:", debug_out(__VA_ARGS__)
struct node{
    string a,b;
    int time;
};
signed main() {
    ios::sync_with_stdio(false);
    cin.tie(0);
    ifstream fs("1.txt");
    string a, b;
    string s;
    vector<node> v;
    vector<int>seq;
    while (fs >> a >> b >> s) {
        v.push_back({a,b,stoll(s)});
    }
    int ret = 1;
    int ans = 1;
    int cur_time = v.at(0).time;
    // debug(v);
    int ss=0;
    for (int i = 1; i < v.size(); i++) {
        int now_time = v[i].time;
        if (now_time - cur_time <= 1000 && (v[i].a==v[i].b)) {
            cur_time = now_time;
            ans++;
            if(ans>=ret)ss++;
            ret = max(ret, ans);
            seq.push_back(cur_time);
        }
        else {
            cur_time = now_time;
            ans=1;
            debug(seq);
            seq.clear();
            seq.push_back(cur_time);
        }
    }
    cout<<ret;
    debug(ret,ss);
    return 0;
}
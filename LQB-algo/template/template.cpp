#include <bits/stdc++.h>
#include <string>
#include <vector>
using namespace std;
#define int long long

string to_string(string s) {
    return '"' + s + '"';
}
string to_string(char c) {
    string res = "'";
    res += c;
    res += "'";
    return res;
}
string to_string(const char *ch) {
    return to_string((string)ch);
}
string to_string(bool bl) {
    return bl ? "true" : "false";
}
template <typename A, typename B>
string to_string(pair<A, B> pr) {
    return "(" + to_string(pr.first) + "," + pr.second + ")";
}
template <typename A>
string to_string(A v) {
    bool tag = 1;
    string res = "{";
    for (const auto &x : v) {
        if (!tag) {
            res += ", ";
        }
        tag = 0;
        res += to_string(x);
    }
    res += "}";
    return res;
}
void debug_out() {
    cout << endl;
}
template <typename Head, typename... Tail>
void debug_out(Head H, Tail... T) {
    cout << ' ' << to_string(H);
    debug_out(T...);
}
#define debug(...)                        \
    cout << "[" << #__VA_ARGS__ << "]: "; \
    debug_out(__VA_ARGS__);
signed main() {
    int a = 1;
    vector<int> v = {1, 4, 5, 6};
    string s = "hahaha";
    debug(a, v, s);
}

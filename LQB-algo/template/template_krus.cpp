#include <algorithm>
#include <bits/stdc++.h>
#define rep(i, a, b) for (int i = a; i < b; i++)
using namespace std;
const int maxn = 1e5 + 5;
int fa[maxn];
int find(int node) {
    if (fa[node] != node) {
        fa[node] = find(fa[node]);
    }
    return fa[node];
}
void merge(int a, int b) {
    if (find(a) != find(b)) {
        fa[find(a)] = find(b);
    }
}
struct node {
    int from, to, val;
};
int main() {
    int nodes, eds;
    cin >> nodes >> eds;
    vector<node> v(eds);
    rep(i, 1, nodes + 1) {
        fa[i] = i;
    }
    rep(i, 0, eds) {
        int from, to, val;
        cin >> from >> to >> val;
        v[i] = {from, to, val};
    }
    auto cmp = [&](node a, node b) {
        return a.val < b.val;
    };
    sort(v.begin(), v.end(), cmp);
    int cost = 0, cnt_merge = 0;
    for (auto ed : v) {
        int from = ed.from;
        int to = ed.to;
        int val = ed.val;
        if (find(from) != find(to)) {
            merge(from, to);
            cost += val;
            cnt_merge++;
        }
    }
    if (cnt_merge == nodes - 1)
        cout << cost;
    else
        cout << "impossible";
}
